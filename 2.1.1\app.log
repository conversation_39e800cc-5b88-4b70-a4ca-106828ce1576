2025-05-30 23:07:41,645 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 23:07:41,648 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-30 23:07:41,649 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:41,652 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:41,677 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:41,677 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:48,080 - INFO - API密钥验证成功
2025-05-30 23:07:48,108 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:48,109 - INFO - 已将加密的API密钥保存到配置文件
2025-05-30 23:07:48,109 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:48,182 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-30 23:07:48,183 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:07:48,242 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:07:48,371 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:07:48,372 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:07:48,373 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:07:48,509 - INFO - 语言模式配置已加载。
2025-05-30 23:07:48,509 - INFO - 语言模式配置已加载。
2025-05-30 23:07:48,510 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 23:07:48,510 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 23:07:48,522 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-30 23:07:48,532 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 23:07:48,533 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:07:48,534 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:07:48,625 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 23:07:49,241 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:07:49,476 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:07:49,476 - INFO - API服务正常
2025-05-30 23:07:51,712 - INFO - 用户输入: 2
2025-05-30 23:07:51,737 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:51,738 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-30 23:07:51,854 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:07:51,857 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:07:53,264 - INFO - 用户输入: 0
2025-05-30 23:07:58,516 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:07:58,517 - INFO - 🔧 调试模式已开启 - 将显示详细的翻译信息
2025-05-30 23:08:01,538 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:08:01,539 - INFO - 思考预算已更新为：0 Tokens（思考模式已关闭）
2025-05-30 23:08:06,932 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:08:06,933 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:08:45,272 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:08:45,273 - INFO - 【原文】
믿지못할 사람들을 믿은거 같아요.. 제가 너무 순진했나봐요..

사람들이 저에게 접근을 했거든요.. 그냥 친하게 지내고 싶어서 그런줄 알았어요...

그런데 이들이 사기꾼인걸 몰랐죠..

다들 저를 공격하고 혼란스럽게 만들고 저를 압박하기 시작했어요./..

그렇게 몰리다보니 어느세.. 제가 돈을 써서라도 뭔가를 해내야된다고 움직이고 있었어요..

그게 다 노림수엿다는것을 나중에 알게 됬어요..

그렇게 저는 전재산을 서울에서 탕진하고 지금은 집으로 돌아왔어요..
2025-05-30 23:08:45,276 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3729.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:08:45,276 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.9)]
2025-05-30 23:08:45,277 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 23:08:45,277 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 23:08:45,277 - INFO - 检测到原文语言: ko
2025-05-30 23:08:45,277 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 23:08:45,277 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 23:08:45,277 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 23:08:45,277 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 23:08:45,278 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 23:08:45,278 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 23:08:45,278 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
믿지못할 사람들을 믿은거 같아요.. 제가 너무 순진했나봐요.. 사람들이 저에게 접근을 했거든요.. 그냥 친하게 지내고 싶어서 그런줄 알았어요... 그런데 이들이 사기꾼인걸 몰랐죠.. 다들 저를 공격하고 혼란스럽게 만들고 저를 압박하기 시작했어요./.. 그렇게 몰리다보니 어느세.. 제가 돈을 써서라도 뭔가를 해내야된다고 움직이고 있었어요.. 그게 다 노림수엿다는것을 나중에 알게 됬어요.. 그렇게 저는 전재산을 서울에서 탕진하고 지금은 집으로 돌아왔어요..
2025-05-30 23:08:45,278 - DEBUG - 【构建提示词】长度: 841 字符
2025-05-30 23:08:45,278 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-30 23:08:45,297 - INFO - 🔄 显示GUI进度指示器
2025-05-30 23:08:45,543 - DEBUG - API密钥解密成功
2025-05-30 23:08:45,543 - DEBUG - API密钥解密成功
2025-05-30 23:08:45,543 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 23:08:45,543 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 23:08:45,544 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 23:08:45,556 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-30 23:08:45,557 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-30 23:08:45,557 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n믿지못할 사람들을 믿은거 같아요.. 제가 너무 순진했나봐요.. 사람들이 저에게 접근을 했거든요.. 그냥 친하게 지내고 싶어서 그런줄 알았어요... 그런데 이들이 사기꾼인걸 몰랐죠.. 다들 저를 공격하고 혼란스럽게 만들고 저를 압박하기 시작했어요./.. 그렇게 몰리다보니 어느세.. 제가 돈을 써서라도 뭔가를 해내야된다고 움직이고 있었어요.. 그게 다 노림수엿다는것을 나중에 알게 됬어요.. 그렇게 저는 전재산을 서울에서 탕진하고 지금은 집으로 돌아왔어요.."
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64,
    "thinkingConfig": {
      "thinkingBudget": 0
    }
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-30 23:08:47,369 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "我好像相信了不值得信任的人... 我可能太天真了... 有人接近我... 我以为他们只是想和我好好相处... 但我不知道他们是骗子... 他们开始攻击我，让我感到困惑，并对我施压... 这样一来，不知不觉中... 我开始行动，即使花钱也要做点什么... 后来我才知道，这一切都是他们的圈套... 就这样，我在首尔花光了所有的钱，现在回到了家..."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 527,
    "candidatesTokenCount": 103,
    "totalTokenCount": 630,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 527
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "77s5aO3uC8Cq1MkPt77okAk"
}

2025-05-30 23:08:47,370 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '我好像相信了不值得信任的人... 我可能太天真了... 有人接近我... 我以为他们只是想和我好好相处... 但我不知道他们是骗子... 他们开始攻击我，让我感到困惑，并对我施压... 这样一来，不知不觉中... 我开始行动，即使花钱也要做点什么... 后来我才知道，这一切都是他们的圈套... 就这样，我在首尔花光了所有的钱，现在回到了家...'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 527, 'candidatesTokenCount': 103, 'totalTokenCount': 630, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 527}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': '77s5aO3uC8Cq1MkPt77okAk'}
2025-05-30 23:08:47,371 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 23:08:47,371 - DEBUG -   - 思考Token数: 0
2025-05-30 23:08:47,371 - DEBUG -   - 提示Token数: 527
2025-05-30 23:08:47,371 - DEBUG -   - 输出Token数: 103
2025-05-30 23:08:47,372 - DEBUG -   - 总Token数: 630
2025-05-30 23:08:47,372 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 99, 1924.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:08:47,373 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.913)]
2025-05-30 23:08:47,373 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 23:08:47,373 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 23:08:47,373 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 23:08:47,374 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 23:08:47,831 - DEBUG - 输入框内容已替换为: 我好像相信了不值得信任的人... 我可能太天真了... 有人接近我... 我以为他们只是想和我好好相处... 但我不知道他们是骗子... 他们开始攻击我，让我感到困惑，并对我施压... 这样一来，不知不觉中... 我开始行动，即使花钱也要做点什么... 后来我才知道，这一切都是他们的圈套... 就这样，我在首尔花光了所有的钱，现在回到了家...
2025-05-30 23:08:47,832 - INFO - 【翻译结果】
我好像相信了不值得信任的人... 我可能太天真了... 有人接近我... 我以为他们只是想和我好好相处... 但我不知道他们是骗子... 他们开始攻击我，让我感到困惑，并对我施压... 这样一来，不知不觉中... 我开始行动，即使花钱也要做点什么... 后来我才知道，这一切都是他们的圈套... 就这样，我在首尔花光了所有的钱，现在回到了家...
2025-05-30 23:08:47,833 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 23:08:47,833 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 23:08:47,861 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 23:08:47,863 - INFO - 已立即保存 2 条缓存记录
2025-05-30 23:08:47,866 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 23:08:48,365 - INFO - ✅ 隐藏GUI进度指示器
2025-05-30 23:09:24,426 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:09:24,426 - INFO - 【原文】
啊？他们让你创业么？
2025-05-30 23:09:24,427 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2166.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:09:24,428 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.912)]
2025-05-30 23:09:24,428 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 23:09:24,428 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 23:09:24,428 - INFO - 检测到原文语言: zh
2025-05-30 23:09:24,429 - INFO - 执行正向翻译为: ko
2025-05-30 23:09:24,429 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 23:09:24,430 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 23:09:24,430 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 23:09:24,430 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 23:09:24,430 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-30 23:09:24,431 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 믿지못할 사람들을 믿은거 같아요.. 제가 너무 순진했나봐요..

사람들이 저에게 접근을 했거든요.. 그냥 친하게 지내고 싶어서 그런줄 알았어요...

그런데 이들이 사기꾼인걸 몰랐죠..

다들 저를 공격하고 혼란스럽게 만들고 저를 압박하기 시작했어요./..

그렇게 몰리다보니 어느세.. 제가 돈을 써서라도 뭔가를 해내야된다고 움직이고 있었어요..

그게 다 노림수엿다는것을 나중에 알게 됬어요..

그렇게 저는 전재산을 서울에서 탕진하고 지금은 집으로 돌아왔어요..
翻译: 我好像相信了不值得信任的人... 我可能太天真了... 有人接近我... 我以为他们只是想和我好好相处... 但我不知道他们是骗子... 他们开始攻击我，让我感到困惑，并对我施压... 这样一来，不知不觉中... 我开始行动，即使花钱也要做点什么... 后来我才知道，这一切都是他们的圈套... 就这样，我在首尔花光了所有的钱，现在回到了家...

将以下内容从中文翻译成韩文，使用敬语：
啊？他们让你创业么？
2025-05-30 23:09:24,431 - DEBUG - 【构建提示词】长度: 1077 字符
2025-05-30 23:09:24,431 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-30 23:09:24,434 - INFO - 🔄 显示GUI进度指示器
2025-05-30 23:09:24,647 - DEBUG - API密钥解密成功
2025-05-30 23:09:24,647 - DEBUG - API密钥解密成功
2025-05-30 23:09:24,648 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 23:09:24,648 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 23:09:24,648 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 23:09:24,649 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-30 23:09:24,649 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-30 23:09:24,650 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语\n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 믿지못할 사람들을 믿은거 같아요.. 제가 너무 순진했나봐요..\r\n사람들이 저에게 접근을 했거든요.. 그냥 친하게 지내고 싶어서 그런줄 알았어요...\r\n그런데 이들이 사기꾼인걸 몰랐죠..\r\n다들 저를 공격하고 혼란스럽게 만들고 저를 압박하기 시작했어요./..\r\n그렇게 몰리다보니 어느세.. 제가 돈을 써서라도 뭔가를 해내야된다고 움직이고 있었어요..\r\n그게 다 노림수엿다는것을 나중에 알게 됬어요..\r\n그렇게 저는 전재산을 서울에서 탕진하고 지금은 집으로 돌아왔어요..\n翻译: 我好像相信了不值得信任的人... 我可能太天真了... 有人接近我... 我以为他们只是想和我好好相处... 但我不知道他们是骗子... 他们开始攻击我，让我感到困惑，并对我施压... 这样一来，不知不觉中... 我开始行动，即使花钱也要做点什么... 后来我才知道，这一切都是他们的圈套... 就这样，我在首尔花光了所有的钱，现在回到了家...\n\n将以下内容从中文翻译成韩文，使用敬语：\n啊？他们让你创业么？"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64,
    "thinkingConfig": {
      "thinkingBudget": 0
    }
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-30 23:09:26,411 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아? 그들이 당신에게 창업하라고 했나요?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 682,
    "candidatesTokenCount": 13,
    "totalTokenCount": 695,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 682
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "Frw5aI7aEo6t1MkPprC9sQk"
}

2025-05-30 23:09:26,412 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아? 그들이 당신에게 창업하라고 했나요?'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 682, 'candidatesTokenCount': 13, 'totalTokenCount': 695, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 682}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'Frw5aI7aEo6t1MkPprC9sQk'}
2025-05-30 23:09:26,412 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 23:09:26,413 - DEBUG -   - 思考Token数: 0
2025-05-30 23:09:26,413 - DEBUG -   - 提示Token数: 682
2025-05-30 23:09:26,413 - DEBUG -   - 输出Token数: 13
2025-05-30 23:09:26,413 - DEBUG -   - 总Token数: 695
2025-05-30 23:09:26,414 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3709.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:09:26,415 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.904)]
2025-05-30 23:09:26,415 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 23:09:26,416 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 23:09:26,416 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 23:09:26,416 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 23:09:26,874 - DEBUG - 输入框内容已替换为: 아? 그들이 당신에게 창업하라고 했나요?
2025-05-30 23:09:26,875 - INFO - 【翻译结果】
아? 그들이 당신에게 창업하라고 했나요?
2025-05-30 23:09:26,875 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 23:09:26,875 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 23:09:26,879 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 23:09:26,881 - INFO - 已立即保存 2 条缓存记录
2025-05-30 23:09:26,884 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 23:09:27,394 - INFO - ✅ 隐藏GUI进度指示器
