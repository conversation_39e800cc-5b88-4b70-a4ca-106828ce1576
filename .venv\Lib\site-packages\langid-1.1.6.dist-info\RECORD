../../Scripts/langid.exe,sha256=yndC0qNYeNQ6_jzEz44jH1sz059UdW0H3zyWJTv4U7M,108437
langid-1.1.6.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
langid-1.1.6.dist-info/METADATA,sha256=rvg59DpwR3q31ABLzvV7xE9mHhTq8c21RCGMiC7-zcU,931
langid-1.1.6.dist-info/RECORD,,
langid-1.1.6.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langid-1.1.6.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
langid-1.1.6.dist-info/entry_points.txt,sha256=BmTKuAMTw5f13fY-ZhklC6khYJ_aZL7Nt5MRfgVlzt0,46
langid-1.1.6.dist-info/top_level.txt,sha256=OfSlJr4_TTCwya2kWzRdtfn_LNWt-eLlijvOiZCJU5k,7
langid/__init__.py,sha256=Y3I3fwS3wE_acSUqoccsrwaTcBElvobs0sQ8YJuJjrs,50
langid/__pycache__/__init__.cpython-313.pyc,,
langid/__pycache__/langid.cpython-313.pyc,,
langid/langid.py,sha256=Xk1JkZPIL33mw4NRCbtmMda8Ge-L3FYFhjjY9ndy9lU,2529444
langid/tools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langid/tools/__pycache__/__init__.cpython-313.pyc,,
langid/tools/__pycache__/featWeights.cpython-313.pyc,,
langid/tools/__pycache__/printfeats.cpython-313.pyc,,
langid/tools/featWeights.py,sha256=8i9H8x4wYoAnBXNppXx1Pmzo9FvBwecB7Dv6aLRg-qc,4153
langid/tools/printfeats.py,sha256=ZqrdJgtWewni-q_x1A696RLFOdVmmdzmYmWQ-yiisU4,1464
langid/train/BLweight.py,sha256=klKSnQYozDPG7HymF2fxG_wix9aPjtou9BtKn823Sn0,4726
langid/train/DFfeatureselect.py,sha256=55ThtoskUzFqNOF3oSMMKX_zSlDi44gGplXojg5R0No,6906
langid/train/IGweight.py,sha256=usqn7S218N1fR-kR5qRlZGOZXE0Gzy2FGVEc7pCUgQA,9397
langid/train/LDfeatureselect.py,sha256=YfrYo_f6nIHSTD0OG90tTD02xtQhYS2mJfIYEc8a3CA,4805
langid/train/NBtrain.py,sha256=juz6kuf47PkY34dD0xZyccDuIm6aEYFA90-FZeKaHjw,11543
langid/train/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
langid/train/__pycache__/__init__.cpython-313.pyc,,
langid/train/__pycache__/common.cpython-313.pyc,,
langid/train/common.py,sha256=Y0g48O0xkhnUwHimplfACv7ahb9KTAN90nxkhBeR3Js,3590
langid/train/index.py,sha256=GOatjO-7HGoMjc1wEHiyZtVv_ENL_BcyEzWNttjCidc,10628
langid/train/scanner.py,sha256=umYyanDd7zZZPBawdL-mmlFybUyEWd67OmUYU8xta80,8690
langid/train/tokenize.py,sha256=jTMK00uaannUY047yM_9CmFpwL9qhSQqWv4sKMz1-d4,13469
langid/train/train.py,sha256=C5zhfFuYMvLHTeQuQFlXMvu9xHkQ4eiibrD9kPIAx54,13409
